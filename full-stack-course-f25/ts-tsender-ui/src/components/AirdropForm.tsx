"use client"

import Input<PERSON>ield from "@/components/ui/InputField"
import { useState, useMemo } from "react"
import { chainsToTSender, erc20Abi, tsender<PERSON>bi } from "@/constants";
import { useChainId, useConfig, useAccount, useWriteContract } from "wagmi"
import { readContract, waitForTransactionReceipt } from "@wagmi/core";
import { calculateTotal } from "@/utils/calculateTotal/calculateTotal";

export default function AirdropForm() {
    // state hooks
    // re-render when the variable changes
    const [tokenAddress, setTokenAddress] = useState("");
    const [recipients, setRecipients] = useState("");
    const [amounts, setAmounts] = useState("");
    const chainId = useChainId();
    const config = useConfig();
    const account = useAccount();
    const total: number = useMemo(() => calculateTotal(amounts), [amounts]); // anytime amounts changes call the function
    const {data: hash, isPending, writeContractAsync} = useWriteContract();


    async function getApprovedAmount(tSenderAddress: String | null): Promise<number> {
        if (!tSenderAddress) {
            alert("No address found")
            return 0
        }

        const response = await readContract(config, {
            abi: erc20Abi,
            address: tokenAddress as `0x${string}`,
            functionName: 'allowance',
            args: [account.address, tSenderAddress as `0x${string}`],
        })
        return response as number
    }

    async function handleSubmit() {
        const tSenderAddress = chainsToTSender[chainId].tsender;
        const approvedAmount = await getApprovedAmount(tSenderAddress);

        if (approvedAmount < total) {
            const approvalHash = await writeContractAsync({
                abi: erc20Abi,
                address: tokenAddress as `0x${string}`,
                functionName: 'approve',
                args: [tSenderAddress as `0x${string}`, BigInt(total)],
            })
            const approvalReceipt = await waitForTransactionReceipt(config, {
                hash: approvalHash
            })

            console.log("Approval receipt", approvalReceipt);
        } else {
            await writeContractAsync({
                abi: tsenderAbi,
                address: tSenderAddress as `0x${string}`,
                functionName: 'sendTokens',
                args: [recipients, amounts],
            })
        }
    }

    return (
        <div>
            <InputField
                label="Token Address"
                placeholder="0x123"
                value={tokenAddress}
                onChange={(e) => setTokenAddress(e.target.value)}
            />
            <InputField
                label="Recipients Addresses"
                placeholder="0x123,0x123,0x123"
                value={recipients}
                large={true}
                onChange={(e) => setRecipients(e.target.value)}
            />
            <InputField
                label="Amounts"
                placeholder="100,200,300"
                value={amounts}
                large={true}
                onChange={(e) => setAmounts(e.target.value)}
            />
            <br></br>
            <button onClick={handleSubmit} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Send Tokens
            </button>
        </div>
    );
}