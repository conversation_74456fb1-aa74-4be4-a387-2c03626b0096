{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/Projects/foundry-course/full-stack-course-f25/ts-tsender-ui/src/components/ui/InputField.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype InputFieldProps = {\n  label: string;\n  placeholder?: string;\n  value: string;\n  type?: string;\n  large?: boolean;\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;\n};\n\nexport default function InputField({\n  label,\n  placeholder = \"\",\n  value,\n  type = \"text\",\n  large = false,\n  onChange,\n}: InputFieldProps) {\n  return (\n    <div className=\"flex flex-col space-y-1\">\n      <label className=\"text-sm font-medium text-gray-700\">{label}</label>\n      {large ? (\n        <textarea\n          placeholder={placeholder}\n          value={value}\n          onChange={onChange}\n          rows={3}\n          className=\"w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-3 text-lg resize-none\"\n        />\n      ) : (\n        <input\n          type={type}\n          placeholder={placeholder}\n          value={value}\n          onChange={onChange}\n          className=\"w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-sm\"\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAWe,SAAS,WAAW,EACjC,KAAK,EACL,cAAc,EAAE,EAChB,KAAK,EACL,OAAO,MAAM,EACb,QAAQ,KAAK,EACb,QAAQ,EACQ;IAChB,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAM,WAAU;0BAAqC;;;;;;YACrD,sBACC,6WAAC;gBACC,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,WAAU;;;;;qCAGZ,6WAAC;gBACC,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,WAAU;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/Projects/foundry-course/full-stack-course-f25/ts-tsender-ui/src/constants.ts"], "sourcesContent": ["interface ContractsConfig {\n    [chainId: number]: {\n        tsender: string\n        no_check: string | null\n    }\n}\n\nexport const chainsToTSender: ContractsConfig = {\n    324: {\n        tsender: \"0x7e645Ea4386deb2E9e510D805461aA12db83fb5E\",\n        no_check: null,\n    },\n    1: {\n        tsender: \"0x3aD9F29AB266E4828450B33df7a9B9D7355Cd821\",\n        no_check: \"0x7D4a746Cb398e5aE19f6cBDC08473664ADBc6da5\",\n    },\n    42161: {\n        tsender: \"0xA2b5aEDF7EEF6469AB9cBD99DE24a6881702Eb19\",\n        no_check: \"0x091bAB6497F2Cc429c82c5807Df4faA34235Cccc\",\n    },\n    10: {\n        tsender: \"0xAaf523DF9455cC7B6ca5637D01624BC00a5e9fAa\",\n        no_check: \"0xa0c7ADA2c7c29729d12e2649BC6a0a293Ac46725\",\n    },\n    8453: {\n        tsender: \"0x31801c3e09708549c1b2c9E1CFbF001399a1B9fa\",\n        no_check: \"0x39338138414Df90EC67dC2EE046ab78BcD4F56D9\",\n    },\n    31337: {\n        tsender: \"0x5FbDB2315678afecb367f032d93F642f64180aa3\",\n        no_check: \"0x5FbDB2315678afecb367f032d93F642f64180aa3\",\n    },\n    11155111: {\n        tsender: \"0xa27c5C77DA713f410F9b15d4B0c52CAe597a973a\",\n        no_check: \"0xa27c5C77DA713f410F9b15d4B0c52CAe597a973a\",\n    }\n}\n\nexport const erc20Abi = [\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"owner\", type: \"address\" },\n            { indexed: true, internalType: \"address\", name: \"spender\", type: \"address\" },\n            { indexed: false, internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n        ],\n        name: \"Approval\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"authorizer\", type: \"address\" },\n            { indexed: true, internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n        ],\n        name: \"AuthorizationCanceled\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"authorizer\", type: \"address\" },\n            { indexed: true, internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n        ],\n        name: \"AuthorizationUsed\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [{ indexed: true, internalType: \"address\", name: \"_account\", type: \"address\" }],\n        name: \"Blacklisted\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"newBlacklister\", type: \"address\" },\n        ],\n        name: \"BlacklisterChanged\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"burner\", type: \"address\" },\n            { indexed: false, internalType: \"uint256\", name: \"amount\", type: \"uint256\" },\n        ],\n        name: \"Burn\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"newMasterMinter\", type: \"address\" },\n        ],\n        name: \"MasterMinterChanged\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"minter\", type: \"address\" },\n            { indexed: true, internalType: \"address\", name: \"to\", type: \"address\" },\n            { indexed: false, internalType: \"uint256\", name: \"amount\", type: \"uint256\" },\n        ],\n        name: \"Mint\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"minter\", type: \"address\" },\n            {\n                indexed: false,\n                internalType: \"uint256\",\n                name: \"minterAllowedAmount\",\n                type: \"uint256\",\n            },\n        ],\n        name: \"MinterConfigured\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [{ indexed: true, internalType: \"address\", name: \"oldMinter\", type: \"address\" }],\n        name: \"MinterRemoved\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: false, internalType: \"address\", name: \"previousOwner\", type: \"address\" },\n            { indexed: false, internalType: \"address\", name: \"newOwner\", type: \"address\" },\n        ],\n        name: \"OwnershipTransferred\",\n        type: \"event\",\n    },\n    { anonymous: false, inputs: [], name: \"Pause\", type: \"event\" },\n    {\n        anonymous: false,\n        inputs: [{ indexed: true, internalType: \"address\", name: \"newAddress\", type: \"address\" }],\n        name: \"PauserChanged\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [{ indexed: true, internalType: \"address\", name: \"newRescuer\", type: \"address\" }],\n        name: \"RescuerChanged\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [\n            { indexed: true, internalType: \"address\", name: \"from\", type: \"address\" },\n            { indexed: true, internalType: \"address\", name: \"to\", type: \"address\" },\n            { indexed: false, internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n        ],\n        name: \"Transfer\",\n        type: \"event\",\n    },\n    {\n        anonymous: false,\n        inputs: [{ indexed: true, internalType: \"address\", name: \"_account\", type: \"address\" }],\n        name: \"UnBlacklisted\",\n        type: \"event\",\n    },\n    { anonymous: false, inputs: [], name: \"Unpause\", type: \"event\" },\n    {\n        inputs: [],\n        name: \"CANCEL_AUTHORIZATION_TYPEHASH\",\n        outputs: [{ internalType: \"bytes32\", name: \"\", type: \"bytes32\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"DOMAIN_SEPARATOR\",\n        outputs: [{ internalType: \"bytes32\", name: \"\", type: \"bytes32\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"PERMIT_TYPEHASH\",\n        outputs: [{ internalType: \"bytes32\", name: \"\", type: \"bytes32\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"RECEIVE_WITH_AUTHORIZATION_TYPEHASH\",\n        outputs: [{ internalType: \"bytes32\", name: \"\", type: \"bytes32\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"TRANSFER_WITH_AUTHORIZATION_TYPEHASH\",\n        outputs: [{ internalType: \"bytes32\", name: \"\", type: \"bytes32\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"owner\", type: \"address\" },\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n        ],\n        name: \"allowance\",\n        outputs: [{ internalType: \"uint256\", name: \"\", type: \"uint256\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n        ],\n        name: \"approve\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"authorizer\", type: \"address\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n        ],\n        name: \"authorizationState\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"account\", type: \"address\" }],\n        name: \"balanceOf\",\n        outputs: [{ internalType: \"uint256\", name: \"\", type: \"uint256\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"_account\", type: \"address\" }],\n        name: \"blacklist\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"blacklister\",\n        outputs: [{ internalType: \"address\", name: \"\", type: \"address\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"uint256\", name: \"_amount\", type: \"uint256\" }],\n        name: \"burn\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"authorizer\", type: \"address\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"uint8\", name: \"v\", type: \"uint8\" },\n            { internalType: \"bytes32\", name: \"r\", type: \"bytes32\" },\n            { internalType: \"bytes32\", name: \"s\", type: \"bytes32\" },\n        ],\n        name: \"cancelAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"authorizer\", type: \"address\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"bytes\", name: \"signature\", type: \"bytes\" },\n        ],\n        name: \"cancelAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"minter\", type: \"address\" },\n            { internalType: \"uint256\", name: \"minterAllowedAmount\", type: \"uint256\" },\n        ],\n        name: \"configureMinter\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"currency\",\n        outputs: [{ internalType: \"string\", name: \"\", type: \"string\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"decimals\",\n        outputs: [{ internalType: \"uint8\", name: \"\", type: \"uint8\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n            { internalType: \"uint256\", name: \"decrement\", type: \"uint256\" },\n        ],\n        name: \"decreaseAllowance\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n            { internalType: \"uint256\", name: \"increment\", type: \"uint256\" },\n        ],\n        name: \"increaseAllowance\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"string\", name: \"tokenName\", type: \"string\" },\n            { internalType: \"string\", name: \"tokenSymbol\", type: \"string\" },\n            { internalType: \"string\", name: \"tokenCurrency\", type: \"string\" },\n            { internalType: \"uint8\", name: \"tokenDecimals\", type: \"uint8\" },\n            { internalType: \"address\", name: \"newMasterMinter\", type: \"address\" },\n            { internalType: \"address\", name: \"newPauser\", type: \"address\" },\n            { internalType: \"address\", name: \"newBlacklister\", type: \"address\" },\n            { internalType: \"address\", name: \"newOwner\", type: \"address\" },\n        ],\n        name: \"initialize\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"string\", name: \"newName\", type: \"string\" }],\n        name: \"initializeV2\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"lostAndFound\", type: \"address\" }],\n        name: \"initializeV2_1\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address[]\", name: \"accountsToBlacklist\", type: \"address[]\" },\n            { internalType: \"string\", name: \"newSymbol\", type: \"string\" },\n        ],\n        name: \"initializeV2_2\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"_account\", type: \"address\" }],\n        name: \"isBlacklisted\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"account\", type: \"address\" }],\n        name: \"isMinter\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"masterMinter\",\n        outputs: [{ internalType: \"address\", name: \"\", type: \"address\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"_to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"_amount\", type: \"uint256\" },\n        ],\n        name: \"mint\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"minter\", type: \"address\" }],\n        name: \"minterAllowance\",\n        outputs: [{ internalType: \"uint256\", name: \"\", type: \"uint256\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"name\",\n        outputs: [{ internalType: \"string\", name: \"\", type: \"string\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"owner\", type: \"address\" }],\n        name: \"nonces\",\n        outputs: [{ internalType: \"uint256\", name: \"\", type: \"uint256\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"owner\",\n        outputs: [{ internalType: \"address\", name: \"\", type: \"address\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    { inputs: [], name: \"pause\", outputs: [], stateMutability: \"nonpayable\", type: \"function\" },\n    {\n        inputs: [],\n        name: \"paused\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"pauser\",\n        outputs: [{ internalType: \"address\", name: \"\", type: \"address\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"owner\", type: \"address\" },\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"deadline\", type: \"uint256\" },\n            { internalType: \"bytes\", name: \"signature\", type: \"bytes\" },\n        ],\n        name: \"permit\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"owner\", type: \"address\" },\n            { internalType: \"address\", name: \"spender\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"deadline\", type: \"uint256\" },\n            { internalType: \"uint8\", name: \"v\", type: \"uint8\" },\n            { internalType: \"bytes32\", name: \"r\", type: \"bytes32\" },\n            { internalType: \"bytes32\", name: \"s\", type: \"bytes32\" },\n        ],\n        name: \"permit\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"from\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validAfter\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validBefore\", type: \"uint256\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"bytes\", name: \"signature\", type: \"bytes\" },\n        ],\n        name: \"receiveWithAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"from\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validAfter\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validBefore\", type: \"uint256\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"uint8\", name: \"v\", type: \"uint8\" },\n            { internalType: \"bytes32\", name: \"r\", type: \"bytes32\" },\n            { internalType: \"bytes32\", name: \"s\", type: \"bytes32\" },\n        ],\n        name: \"receiveWithAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"minter\", type: \"address\" }],\n        name: \"removeMinter\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"contract IERC20\", name: \"tokenContract\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"amount\", type: \"uint256\" },\n        ],\n        name: \"rescueERC20\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"rescuer\",\n        outputs: [{ internalType: \"address\", name: \"\", type: \"address\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"symbol\",\n        outputs: [{ internalType: \"string\", name: \"\", type: \"string\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"totalSupply\",\n        outputs: [{ internalType: \"uint256\", name: \"\", type: \"uint256\" }],\n        stateMutability: \"view\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n        ],\n        name: \"transfer\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"from\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n        ],\n        name: \"transferFrom\",\n        outputs: [{ internalType: \"bool\", name: \"\", type: \"bool\" }],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"newOwner\", type: \"address\" }],\n        name: \"transferOwnership\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"from\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validAfter\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validBefore\", type: \"uint256\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"bytes\", name: \"signature\", type: \"bytes\" },\n        ],\n        name: \"transferWithAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [\n            { internalType: \"address\", name: \"from\", type: \"address\" },\n            { internalType: \"address\", name: \"to\", type: \"address\" },\n            { internalType: \"uint256\", name: \"value\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validAfter\", type: \"uint256\" },\n            { internalType: \"uint256\", name: \"validBefore\", type: \"uint256\" },\n            { internalType: \"bytes32\", name: \"nonce\", type: \"bytes32\" },\n            { internalType: \"uint8\", name: \"v\", type: \"uint8\" },\n            { internalType: \"bytes32\", name: \"r\", type: \"bytes32\" },\n            { internalType: \"bytes32\", name: \"s\", type: \"bytes32\" },\n        ],\n        name: \"transferWithAuthorization\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"_account\", type: \"address\" }],\n        name: \"unBlacklist\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    { inputs: [], name: \"unpause\", outputs: [], stateMutability: \"nonpayable\", type: \"function\" },\n    {\n        inputs: [{ internalType: \"address\", name: \"_newBlacklister\", type: \"address\" }],\n        name: \"updateBlacklister\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"_newMasterMinter\", type: \"address\" }],\n        name: \"updateMasterMinter\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"_newPauser\", type: \"address\" }],\n        name: \"updatePauser\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [{ internalType: \"address\", name: \"newRescuer\", type: \"address\" }],\n        name: \"updateRescuer\",\n        outputs: [],\n        stateMutability: \"nonpayable\",\n        type: \"function\",\n    },\n    {\n        inputs: [],\n        name: \"version\",\n        outputs: [{ internalType: \"string\", name: \"\", type: \"string\" }],\n        stateMutability: \"pure\",\n        type: \"function\",\n    },\n]\n\nexport const tsenderAbi = [\n    {\n        type: \"function\",\n        name: \"airdropERC20\",\n        inputs: [\n            {\n                name: \"tokenAddress\",\n                type: \"address\",\n                internalType: \"address\",\n            },\n            {\n                name: \"recipients\",\n                type: \"address[]\",\n                internalType: \"address[]\",\n            },\n            {\n                name: \"amounts\",\n                type: \"uint256[]\",\n                internalType: \"uint256[]\",\n            },\n            {\n                name: \"totalAmount\",\n                type: \"uint256\",\n                internalType: \"uint256\",\n            },\n        ],\n        outputs: [],\n        stateMutability: \"nonpayable\",\n    },\n    {\n        type: \"function\",\n        name: \"areListsValid\",\n        inputs: [\n            {\n                name: \"recipients\",\n                type: \"address[]\",\n                internalType: \"address[]\",\n            },\n            {\n                name: \"amounts\",\n                type: \"uint256[]\",\n                internalType: \"uint256[]\",\n            },\n        ],\n        outputs: [\n            {\n                name: \"\",\n                type: \"bool\",\n                internalType: \"bool\",\n            },\n        ],\n        stateMutability: \"pure\",\n    },\n]\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,kBAAmC;IAC5C,KAAK;QACD,SAAS;QACT,UAAU;IACd;IACA,GAAG;QACC,SAAS;QACT,UAAU;IACd;IACA,OAAO;QACH,SAAS;QACT,UAAU;IACd;IACA,IAAI;QACA,SAAS;QACT,UAAU;IACd;IACA,MAAM;QACF,SAAS;QACT,UAAU;IACd;IACA,OAAO;QACH,SAAS;QACT,UAAU;IACd;IACA,UAAU;QACN,SAAS;QACT,UAAU;IACd;AACJ;AAEO,MAAM,WAAW;IACpB;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YACzE;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC3E;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC9E;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC5E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC9E;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC5E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YAAC;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACvF,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAkB,MAAM;YAAU;SACrF;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;YAC1E;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;SAC9E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAmB,MAAM;YAAU;SACtF;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;YAC1E;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACtE;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;SAC9E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;YAC1E;gBACI,SAAS;gBACT,cAAc;gBACd,MAAM;gBACN,MAAM;YACV;SACH;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YAAC;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAa,MAAM;YAAU;SAAE;QACxF,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAiB,MAAM;YAAU;YAClF;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAChF;QACD,MAAM;QACN,MAAM;IACV;IACA;QAAE,WAAW;QAAO,QAAQ,EAAE;QAAE,MAAM;QAAS,MAAM;IAAQ;IAC7D;QACI,WAAW;QACX,QAAQ;YAAC;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;SAAE;QACzF,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YAAC;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;SAAE;QACzF,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YACJ;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACxE;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACtE;gBAAE,SAAS;gBAAO,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7E;QACD,MAAM;QACN,MAAM;IACV;IACA;QACI,WAAW;QACX,QAAQ;YAAC;gBAAE,SAAS;gBAAM,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACvF,MAAM;QACN,MAAM;IACV;IACA;QAAE,WAAW;QAAO,QAAQ,EAAE;QAAE,MAAM;QAAW,MAAM;IAAQ;IAC/D;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;SAC/D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC5D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;SAAE;QACvE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACxE,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;SAAE;QACvE,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAK,MAAM;YAAQ;YAClD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACtD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;SACzD;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAa,MAAM;YAAQ;SAC7D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;YAC3D;gBAAE,cAAc;gBAAW,MAAM;gBAAuB,MAAM;YAAU;SAC3E;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAU,MAAM;gBAAI,MAAM;YAAS;SAAE;QAC/D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAS,MAAM;gBAAI,MAAM;YAAQ;SAAE;QAC7D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC5D;gBAAE,cAAc;gBAAW,MAAM;gBAAa,MAAM;YAAU;SACjE;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC5D;gBAAE,cAAc;gBAAW,MAAM;gBAAa,MAAM;YAAU;SACjE;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAU,MAAM;gBAAa,MAAM;YAAS;YAC5D;gBAAE,cAAc;gBAAU,MAAM;gBAAe,MAAM;YAAS;YAC9D;gBAAE,cAAc;gBAAU,MAAM;gBAAiB,MAAM;YAAS;YAChE;gBAAE,cAAc;gBAAS,MAAM;gBAAiB,MAAM;YAAQ;YAC9D;gBAAE,cAAc;gBAAW,MAAM;gBAAmB,MAAM;YAAU;YACpE;gBAAE,cAAc;gBAAW,MAAM;gBAAa,MAAM;YAAU;YAC9D;gBAAE,cAAc;gBAAW,MAAM;gBAAkB,MAAM;YAAU;YACnE;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAChE;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAU,MAAM;gBAAW,MAAM;YAAS;SAAE;QACrE,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAgB,MAAM;YAAU;SAAE;QAC5E,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAa,MAAM;gBAAuB,MAAM;YAAY;YAC5E;gBAAE,cAAc;gBAAU,MAAM;gBAAa,MAAM;YAAS;SAC/D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACxE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;SAAE;QACvE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAO,MAAM;YAAU;YACxD;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;SAC/D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;SAAE;QACtE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAU,MAAM;gBAAI,MAAM;YAAS;SAAE;QAC/D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAAE;QACrE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QAAE,QAAQ,EAAE;QAAE,MAAM;QAAS,SAAS,EAAE;QAAE,iBAAiB;QAAc,MAAM;IAAW;IAC1F;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC5D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;YAC7D;gBAAE,cAAc;gBAAS,MAAM;gBAAa,MAAM;YAAQ;SAC7D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAW,MAAM;YAAU;YAC5D;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;YAC7D;gBAAE,cAAc;gBAAS,MAAM;gBAAK,MAAM;YAAQ;YAClD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACtD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;SACzD;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACzD;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAe,MAAM;YAAU;YAChE;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAa,MAAM;YAAQ;SAC7D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACzD;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAe,MAAM;YAAU;YAChE;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAK,MAAM;YAAQ;YAClD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACtD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;SACzD;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;SAAE;QACtE,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAmB,MAAM;gBAAiB,MAAM;YAAU;YAC1E;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAU,MAAM;YAAU;SAC9D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAU,MAAM;gBAAI,MAAM;YAAS;SAAE;QAC/D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAI,MAAM;YAAU;SAAE;QACjE,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACzD;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;SAC7D;QACD,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAQ,MAAM;gBAAI,MAAM;YAAO;SAAE;QAC3D,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACxE,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACzD;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAe,MAAM;YAAU;YAChE;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAa,MAAM;YAAQ;SAC7D;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YACJ;gBAAE,cAAc;gBAAW,MAAM;gBAAQ,MAAM;YAAU;YACzD;gBAAE,cAAc;gBAAW,MAAM;gBAAM,MAAM;YAAU;YACvD;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;YAC/D;gBAAE,cAAc;gBAAW,MAAM;gBAAe,MAAM;YAAU;YAChE;gBAAE,cAAc;gBAAW,MAAM;gBAAS,MAAM;YAAU;YAC1D;gBAAE,cAAc;gBAAS,MAAM;gBAAK,MAAM;YAAQ;YAClD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;YACtD;gBAAE,cAAc;gBAAW,MAAM;gBAAK,MAAM;YAAU;SACzD;QACD,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAY,MAAM;YAAU;SAAE;QACxE,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QAAE,QAAQ,EAAE;QAAE,MAAM;QAAW,SAAS,EAAE;QAAE,iBAAiB;QAAc,MAAM;IAAW;IAC5F;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAmB,MAAM;YAAU;SAAE;QAC/E,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAoB,MAAM;YAAU;SAAE;QAChF,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;SAAE;QAC1E,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ;YAAC;gBAAE,cAAc;gBAAW,MAAM;gBAAc,MAAM;YAAU;SAAE;QAC1E,MAAM;QACN,SAAS,EAAE;QACX,iBAAiB;QACjB,MAAM;IACV;IACA;QACI,QAAQ,EAAE;QACV,MAAM;QACN,SAAS;YAAC;gBAAE,cAAc;gBAAU,MAAM;gBAAI,MAAM;YAAS;SAAE;QAC/D,iBAAiB;QACjB,MAAM;IACV;CACH;AAEM,MAAM,aAAa;IACtB;QACI,MAAM;QACN,MAAM;QACN,QAAQ;YACJ;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;SACH;QACD,SAAS,EAAE;QACX,iBAAiB;IACrB;IACA;QACI,MAAM;QACN,MAAM;QACN,QAAQ;YACJ;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;YACA;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;SACH;QACD,SAAS;YACL;gBACI,MAAM;gBACN,MAAM;gBACN,cAAc;YAClB;SACH;QACD,iBAAiB;IACrB;CACH", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/Projects/foundry-course/full-stack-course-f25/ts-tsender-ui/src/utils/calculateTotal/calculateTotal.ts"], "sourcesContent": ["export function calculateTotal(amounts: string): number {\n    return amounts\n      .split(/[\\n,]+/)\n      .map(amt => amt.trim())\n      .filter(amt => amt !== \"\")\n      .map(amt => parseFloat(amt))\n      .filter(n => !isNaN(n))\n      .reduce((sum, n) => sum + n, 0);\n  }\n"], "names": [], "mappings": ";;;AAAO,SAAS,eAAe,OAAe;IAC1C,OAAO,QACJ,KAAK,CAAC,UACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,QAAQ,IACtB,GAAG,CAAC,CAAA,MAAO,WAAW,MACtB,MAAM,CAAC,CAAA,IAAK,CAAC,MAAM,IACnB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG;AACjC", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/Projects/foundry-course/full-stack-course-f25/ts-tsender-ui/src/components/AirdropForm.tsx"], "sourcesContent": ["\"use client\"\n\nimport Input<PERSON><PERSON> from \"@/components/ui/InputField\"\nimport { useState, useMemo } from \"react\"\nimport { chainsToTSender, erc20Abi } from \"@/constants\";\nimport { useChainId, useConfig, useAccount, useWriteContract } from \"wagmi\"\nimport { readContract } from \"@wagmi/core\";\nimport { calculateTotal } from \"@/utils/calculateTotal/calculateTotal\";\n\nexport default function AirdropForm() {\n    // state hooks\n    // re-render when the variable changes\n    const [tokenAddress, setTokenAddress] = useState(\"\");\n    const [recipients, setRecipients] = useState(\"\");\n    const [amounts, setAmounts] = useState(\"\");\n    const chainId = useChainId();\n    const config = useConfig();\n    const account = useAccount();\n    const total: number = useMemo(() => calculateTotal(amounts), [amounts]) // anytime amounts changes call the function\n    const {data}\n\n    async function getApprovedAmount(tSenderAddress: String | null): Promise<number> {\n        if (!tSenderAddress) {\n            alert(\"No address found\")\n            return 0\n        }\n\n        const response = await readContract(config, {\n            abi: erc20Abi,\n            address: tokenAddress as `0x${string}`,\n            functionName: 'allowance',\n            args: [account.address, tSenderAddress as `0x${string}`],\n        })\n        return response as number\n    }\n\n    async function handleSubmit() {\n        const tSenderAddress = chainsToTSender[chainId].tsender;\n        const approvedAmount = await getApprovedAmount(tSenderAddress);\n\n        if (approvedAmount < total) {\n            \n        }\n    }\n\n    return (\n        <div>\n            <InputField\n                label=\"Token Address\"\n                placeholder=\"0x123\"\n                value={tokenAddress}\n                onChange={(e) => setTokenAddress(e.target.value)}\n            />\n            <InputField\n                label=\"Recipients Addresses\"\n                placeholder=\"0x123,0x123,0x123\"\n                value={recipients}\n                large={true}\n                onChange={(e) => setRecipients(e.target.value)}\n            />\n            <InputField\n                label=\"Amounts\"\n                placeholder=\"100,200,300\"\n                value={amounts}\n                large={true}\n                onChange={(e) => setAmounts(e.target.value)}\n            />\n            <br></br>\n            <button onClick={handleSubmit} className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\n                Send Tokens\n            </button>\n        </div>\n    );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACpB,cAAc;IACd,sCAAsC;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,2WAAA,CAAA,aAAU,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,0WAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU,CAAA,GAAA,2WAAA,CAAA,aAAU,AAAD;IACzB,MAAM,QAAgB,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,gJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAAC;KAAQ,EAAE,4CAA4C;;IACpH,MAAM,EAAC,IAAI,EAAC;IAEZ,eAAe,kBAAkB,cAA6B;QAC1D,IAAI,CAAC,gBAAgB;YACjB,MAAM;YACN,OAAO;QACX;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sXAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;YACxC,KAAK,gHAAA,CAAA,WAAQ;YACb,SAAS;YACT,cAAc;YACd,MAAM;gBAAC,QAAQ,OAAO;gBAAE;aAAgC;QAC5D;QACA,OAAO;IACX;IAEA,eAAe;QACX,MAAM,iBAAiB,gHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,OAAO;QACvD,MAAM,iBAAiB,MAAM,kBAAkB;QAE/C,IAAI,iBAAiB,OAAO,CAE5B;IACJ;IAEA,qBACI,6WAAC;;0BACG,6WAAC,sIAAA,CAAA,UAAU;gBACP,OAAM;gBACN,aAAY;gBACZ,OAAO;gBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;0BAEnD,6WAAC,sIAAA,CAAA,UAAU;gBACP,OAAM;gBACN,aAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;0BAEjD,6WAAC,sIAAA,CAAA,UAAU;gBACP,OAAM;gBACN,aAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;0BAE9C,6WAAC;;;;;0BACD,6WAAC;gBAAO,SAAS;gBAAc,WAAU;0BAAuE;;;;;;;;;;;;AAK5H", "debugId": null}}]}